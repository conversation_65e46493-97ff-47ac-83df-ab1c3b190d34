defmodule Drops.Operations do
  @moduledoc """
  Operations module for defining command and query operations.

  This module provides a framework for defining operations that can be used
  to encapsulate business logic with input validation and execution.

  ## Extension Registration

  Extensions can be registered using the `register_extension` macro:

      defmodule MyApp.Operations do
        use Drops.Operations

        register_extension(MyApp.Extensions.Audit)
      end

  """

  require Drops.Operations.Extension

  alias Drops.Operations.{Extension, UnitOfWork}

  defmodule Success do
    @type t :: %__MODULE__{}

    defstruct [:type, :operation, :result, :params]
  end

  defmodule Failure do
    @type t :: %__MODULE__{}

    defstruct [:type, :operation, :result, :params]
  end

  @doc """
  Callback for executing an operation with given context.
  The context is a map that contains at least a :params key.
  """
  @callback execute(context :: map()) :: {:ok, any()} | {:error, any()}

  @doc """
  Callback for preparing parameters before execution.
  Receives context map and should return updated context.
  """
  @callback prepare(context :: map()) :: {:ok, map()} | {:error, any()}

  @doc """
  Callback for validating parameters.
  Receives context map and should return validated context or error.
  """
  @callback validate(context :: map()) :: {:ok, map()} | {:error, any()}

  @optional_callbacks prepare: 1, validate: 1

  @doc """
  Before compile callback to extend UoW after all schema macros have been processed.
  """
  defmacro __before_compile__(env) do
    module = env.module

    opts = Module.get_attribute(module, :opts)
    base_module = Module.get_attribute(module, :base_module)
    schema_meta = Module.get_attribute(module, :schema_meta, %{})

    final_opts = Keyword.put(opts, :schema_meta, schema_meta)
    base_steps = [:conform, :prepare, :validate, :execute]

    steps =
      if has_empty_schema?(module) do
        List.delete(base_steps, :conform)
      else
        base_steps
      end

    # Get extensions from opts (they were set up in __define_operation__)
    registered_extensions = Keyword.get(opts, :extensions, [])

    enabled_extensions = Extension.enabled_extensions(registered_extensions, final_opts)
    extension_code = Extension.extend_operation(enabled_extensions, final_opts)

    base_unit_of_work = UnitOfWork.new(module, steps)

    unit_of_work =
      Drops.Operations.Extension.extend_unit_of_work(
        base_unit_of_work,
        module,
        enabled_extensions,
        final_opts
      )

    Module.put_attribute(module, :unit_of_work, unit_of_work)
    Module.put_attribute(module, :enabled_extensions, enabled_extensions)

    # Add all parent extensions to this module's registered extensions
    if base_module do
      for extension <- registered_extensions do
        Module.put_attribute(module, :registered_extensions, extension)
      end
    end

    quote location: :keep do
      def registered_extensions do
        # Extensions are stored in @opts during compilation
        Keyword.get(@opts, :extensions, [])
      end

      def enabled_extensions, do: @enabled_extensions

      def __unit_of_work__, do: @unit_of_work

      # Inject extension code FIRST so it can override defaults
      unquote_splicing(extension_code)

      def prepare(context) do
        {:ok, context}
      end

      def validate(context) when is_map(context) do
        {:ok, context}
      end

      def execute(_context) do
        raise "#{__MODULE__}.execute/1 must be implemented"
      end
    end
  end

  @doc """
  Register an extension module for this operations module.

  This macro accumulates extension modules in the `:registered_extensions` module attribute.
  When operations are defined using this module as a base, they will automatically
  be extended with the registered extensions.

  ## Parameters

  - `extension` - The extension module to register

  ## Example

      defmodule MyApp.Operations do
        use Drops.Operations

        register_extension(MyApp.Extensions.Audit)
        register_extension(MyApp.Extensions.Cache)
      end
  """

  defmacro steps(do: block) do
    quote do
      @steps unquote(Macro.escape(block))

      def steps, do: @steps
    end
  end

  defmacro __using__(opts) do
    quote do
      import Drops.Operations

      @opts unquote(opts)
      def __opts__, do: @opts

      def registered_extensions do
        Keyword.get(@opts, :extensions, [])
      end

      require Drops.Operations.Extensions.Ecto

      defmacro __using__(opts) when opts == [] do
        # Inherit parent extensions and merge with any new ones
        parent_extensions = Keyword.get(@opts, :extensions, [])
        merged_opts = Keyword.put(@opts, :extensions, parent_extensions)
        # For the root module, base_module is Drops.Operations
        Drops.Operations.__define_operation__(merged_opts, Drops.Operations)
      end

      defmacro __using__(type) when is_atom(type) do
        parent_extensions = Keyword.get(@opts, :extensions, [])

        merged_opts =
          @opts
          |> Keyword.merge(type: type)
          |> Keyword.put(:extensions, parent_extensions)

        Drops.Operations.__define_operation__(merged_opts, __MODULE__)
      end

      defmacro __using__(opts) when is_list(opts) do
        parent_extensions = Keyword.get(@opts, :extensions, [])
        new_extensions = Keyword.get(opts, :extensions, [])
        all_extensions = (parent_extensions ++ new_extensions) |> Enum.uniq()

        merged_opts =
          @opts
          |> Keyword.merge(opts)
          |> Keyword.put(:extensions, all_extensions)

        Drops.Operations.__define_operation__(merged_opts, __MODULE__)
      end
    end
  end

  def __define_operation__(opts, base_module) do
    # Ensure Ecto extension is always included if not already present
    base_opts =
      if base_module && base_module != Drops.Operations,
        do: base_module.__opts__(),
        else: []

    base_extensions = Keyword.get(base_opts, :extensions, [])
    new_extensions = Keyword.get(opts, :extensions, [])

    # Add Ecto extension if this is the root Drops.Operations module
    default_extensions =
      if base_module == Drops.Operations do
        [Drops.Operations.Extensions.Ecto]
      else
        []
      end

    all_extensions =
      (default_extensions ++ base_extensions ++ new_extensions) |> Enum.uniq()

    # Filter extensions by enabled?/1
    enabled_extensions =
      Enum.filter(all_extensions, & &1.enabled?(Keyword.merge(base_opts, opts)))

    final_opts =
      base_opts
      |> Keyword.merge(opts)
      |> Keyword.put(:extensions, enabled_extensions)

    # We'll defer extension resolution to __before_compile__ to ensure parent module is fully compiled
    steps_code =
      if function_exported?(base_module, :steps, 0), do: base_module.steps(), else: []

    # Get functions that extensions will define
    # For now, use a conservative list of common extension functions
    extension_functions = [
      :ecto_schema,
      :repo,
      :changeset,
      :validate_changeset,
      :get_struct,
      :insert,
      :update,
      :delete,
      :persist
    ]

    # Get functions from base module that should be delegated
    base_functions =
      if Code.ensure_loaded?(base_module) do
        base_module.__info__(:functions)
        |> Enum.reject(fn {name, _arity} ->
          # Exclude Contract functions, step functions, internal functions, and extension functions
          name in [
            :schema,
            :schemas,
            :conform,
            :rule,
            :rules,
            :__opts__,
            :registered_extensions
          ] or
            name in [:prepare, :validate, :execute, :finalize, :call] or
            name in extension_functions or
            String.starts_with?(Atom.to_string(name), "__")
        end)
      else
        []
      end

    quote location: :keep do
      import Drops.Operations

      @behaviour Drops.Operations

      use Drops.Contract

      schema do
        %{}
      end

      def conform(%{params: params} = context) when is_map(context) do
        case super(params) do
          {:ok, conformed_params} ->
            {:ok, Map.put(context, :params, conformed_params)}

          {:error, _} = error ->
            error
        end
      end

      require unquote(base_module)

      # Store base module for runtime extension inheritance
      # Only store if it's not the root Drops.Operations module
      @base_module unquote(if base_module != Drops.Operations, do: base_module, else: nil)
      @operation_type unquote(opts[:type])
      @opts unquote(final_opts)
      @schema_opts if unquote(opts[:type]) == :form, do: [atomize: true], else: []

      @before_compile Drops.Operations

      defmacro __using__(opts) when opts == [] do
        Drops.Operations.__define_operation__(@opts, __MODULE__)
      end

      defmacro __using__(type) when is_atom(type) do
        merged_opts = Keyword.merge(@opts, type: type)

        Drops.Operations.__define_operation__(merged_opts, __MODULE__)
      end

      defmacro __using__(opts) when is_list(opts) do
        unless Keyword.has_key?(opts, :type) do
          raise ArgumentError, "type option is required when using Drops.Operations"
        end

        merged_opts = Keyword.merge(@opts, opts)

        Drops.Operations.__define_operation__(merged_opts, __MODULE__)
      end

      def __opts__, do: @opts
      def __operation_type__, do: @operation_type

      def call(context) do
        UnitOfWork.process(__unit_of_work__(), context)
      end

      def call({:ok, previous_result}, context) do
        UnitOfWork.process(
          __unit_of_work__(),
          Map.put(context, :execute_result, previous_result)
        )
      end

      def call({:error, _error} = error_result, _input) do
        error_result
      end

      unquote(steps_code)

      # Delegate functions from base module
      unquote_splicing(
        Enum.map(base_functions, fn {name, arity} ->
          args = Macro.generate_arguments(arity, __MODULE__)

          quote do
            defdelegate unquote(name)(unquote_splicing(args)), to: unquote(base_module)
          end
        end)
      )
    end
  end

  defp has_empty_schema?(operation_module) do
    schemas = Module.get_attribute(operation_module, :schemas, %{})
    default_schema = schemas[:default]

    default_schema == nil or length(default_schema.keys) == 0
  end
end
